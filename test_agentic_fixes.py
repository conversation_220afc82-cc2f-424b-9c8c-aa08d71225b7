#!/usr/bin/env python3
"""
Test script to verify the agentic fixes work correctly.
This script tests:
1. Task creation loop feedback mechanism
2. Summarization error handling
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the aider directory to the path so we can import modules
sys.path.insert(0, str(Path(__file__).parent))

def test_task_creation_feedback():
    """Test that task creation doesn't loop multiple times."""
    print("Testing task creation feedback mechanism...")
    
    try:
        from aider.agentic.agentic_coder import AgenticCoder
        from aider.agentic.task_manager import TaskManager
        from aider.coders.base_coder import Coder
        from aider.io import InputOutput
        from aider import models
        
        # Create a mock coder for testing
        io = InputOutput()
        model = models.Model("gpt-4o-mini")
        coder = Coder(main_model=model, io=io)
        
        # Create agentic coder
        agentic_coder = AgenticCoder(coder, auto_approve=True)
        
        # Test initial state
        assert not agentic_coder._tasks_created_for_session
        assert agentic_coder._current_session_message is None
        
        # Test session state tracking
        test_message = "Create a simple hello world function"
        agentic_coder._current_session_message = test_message
        agentic_coder._tasks_created_for_session = True
        
        # Test reset functionality
        agentic_coder.reset_session_state()
        assert not agentic_coder._tasks_created_for_session
        assert agentic_coder._current_session_message is None
        
        print("✅ Task creation feedback mechanism test passed")
        return True
        
    except Exception as e:
        print(f"❌ Task creation feedback test failed: {e}")
        return False

def test_summarization_error_handling():
    """Test that summarization errors are handled gracefully."""
    print("Testing summarization error handling...")

    try:
        from aider.history import ChatSummary
        from aider import models

        # Create a mock model that will fail
        class FailingModel:
            def __init__(self, name="failing-model"):
                self.name = name

            def token_count(self, message):
                return 10  # Mock token count

            def simple_send_with_retries(self, messages):
                raise RuntimeError("cannot schedule new futures after shutdown")

        # Test with failing model
        failing_model = FailingModel()
        summarizer = ChatSummary([failing_model])

        # This should not raise an exception but return a fallback summary
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]

        result = summarizer.summarize_all(messages)

        # Should return a fallback summary instead of raising an exception
        assert result is not None
        assert len(result) > 0

        print("✅ Summarization error handling test passed")
        return True

    except Exception as e:
        print(f"❌ Summarization error handling test failed: {e}")
        return False

def test_examples_folder_creation():
    """Test creating the examples folder structure for the todo API test."""
    print("Setting up examples folder for testing...")
    
    try:
        examples_dir = Path("examples")
        examples_dir.mkdir(exist_ok=True)
        
        # Create a simple README to indicate this is for testing
        readme_path = examples_dir / "README.md"
        readme_content = """# Examples Directory

This directory is used for testing agentic mode functionality.
"""
        readme_path.write_text(readme_content)
        
        print(f"✅ Examples folder created at: {examples_dir.absolute()}")
        return True
        
    except Exception as e:
        print(f"❌ Examples folder creation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing agentic fixes...")
    print("=" * 50)
    
    tests = [
        test_task_creation_feedback,
        test_summarization_error_handling,
        test_examples_folder_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes should work correctly.")
        print("\nTo test the full agentic workflow, run:")
        print("python -m aider --agentic --message 'Build a simple todo API in the examples folder'")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
