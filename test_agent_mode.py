#!/usr/bin/env python3
"""
Test script to verify the /agent-mode command works correctly.
"""

def add_numbers(a, b):
    """Add two numbers together."""
    return a + b

def multiply_numbers(a, b):
    """Multiply two numbers together."""
    return a * b

if __name__ == "__main__":
    print("Test functions for agent mode testing")
    print(f"add_numbers(2, 3) = {add_numbers(2, 3)}")
    print(f"multiply_numbers(4, 5) = {multiply_numbers(4, 5)}")
