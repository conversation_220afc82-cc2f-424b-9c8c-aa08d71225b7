# Agentic AI Integration for Aider

This module adds agentic capabilities to aider, allowing it to break down complex coding requests into manageable tasks and execute them with human-in-the-loop control.

## Overview

The agentic integration uses [Pydantic AI](https://ai.pydantic.dev/agents/) to create intelligent agents that can:

1. **Decompose** complex coding requests into specific, actionable tasks
2. **Execute** tasks using aider's existing functionality as tools
3. **Monitor** progress and handle failures gracefully
4. **Coordinate** with humans for approval and guidance

## Key Components

### TaskManager
Manages a collection of coding tasks with dependencies and progress tracking.

```python
from aider.agentic import TaskManager, TaskStatus

manager = TaskManager()
task_id = manager.create_task(
    title="Add error handling",
    description="Add try-catch blocks to handle potential errors",
    priority=1,
    dependencies=["setup_task_id"]
)
```

### AgenticCoder
A wrapper around the existing Coder that adds agentic task decomposition and execution.

```python
from aider.agentic import AgenticCoder

# Wrap an existing coder
agentic_coder = AgenticCoder(base_coder, auto_approve=False)

# Run with agentic capabilities
result = await agentic_coder.run_agentic("Create a REST API with authentication")
```

### Agents
Pydantic AI agents for task decomposition and execution:

- **TaskDecompositionAgent**: Analyzes requests and creates task plans
- **TaskExecutionAgent**: Executes individual tasks using aider tools

## Usage

### Command Line

Enable agentic mode with command-line flags:

```bash
# Basic agentic mode
aider --agentic file.py

# With auto-approval (no human confirmation)
aider --agentic --agentic-auto-approve file.py

# With separate model for agentic operations
aider --agentic --agentic-model gpt-4 file.py

# Single message with agentic processing
aider --agentic --message "Add comprehensive error handling and logging" file.py
```

### Programmatic Usage

```python
from aider.coders import Coder
from aider.io import InputOutput

# Create coder with agentic mode enabled
io = InputOutput()
coder = Coder.create(
    main_model=model,
    io=io,
    agentic_mode=True,
    agentic_auto_approve=False
)

# Use normally - agentic mode will activate for complex requests
coder.run(with_message="Refactor this code to use dependency injection")
```

## Workflow

1. **Request Analysis**: User provides a coding request
2. **Task Decomposition**: The decomposition agent breaks it into specific tasks
3. **Plan Review**: Human reviews and approves the task plan (unless auto-approve is enabled)
4. **Task Execution**: Tasks are executed in dependency order
5. **Progress Monitoring**: Human can monitor progress and intervene if needed
6. **Completion**: Final results are presented

## Configuration

### Environment Variables

```bash
# Enable agentic mode by default
export AIDER_AGENTIC=true

# Auto-approve tasks
export AIDER_AGENTIC_AUTO_APPROVE=true

# Separate model for agentic operations
export AIDER_AGENTIC_MODEL=gpt-4
```

### Configuration File

Add to `.aider.conf.yml`:

```yaml
agentic: true
agentic-auto-approve: false
agentic-model: gpt-4
```

## Human-in-the-Loop Controls

The agentic system provides several points for human intervention:

1. **Task Plan Approval**: Review and approve the generated task plan
2. **Task Execution Approval**: Approve each task before execution
3. **Failure Handling**: Choose how to handle task failures (retry, skip, abort)
4. **Progress Monitoring**: View real-time progress and intervene if needed

## Examples

### Simple Task Decomposition

```bash
aider --agentic --message "Add user authentication to this web app"
```

This might create tasks like:
1. Create user model with password hashing
2. Add login/logout routes
3. Implement session management
4. Add authentication middleware
5. Update templates with login forms
6. Add tests for authentication

### Complex Refactoring

```bash
aider --agentic --message "Refactor this monolith into microservices"
```

This might create tasks like:
1. Analyze current architecture and dependencies
2. Identify service boundaries
3. Extract user service
4. Extract product service
5. Extract order service
6. Add API gateway
7. Update configuration and deployment
8. Add integration tests

## Error Handling

The agentic system handles various error scenarios:

- **Task Failures**: Retry, skip, or abort based on user choice
- **Dependency Issues**: Detect and resolve circular dependencies
- **Model Errors**: Fallback to normal aider mode
- **User Interruption**: Graceful shutdown with progress preservation

## Performance Considerations

- **Model Usage**: Agentic mode uses additional model calls for decomposition
- **Task Granularity**: Balance between too many small tasks and too few large tasks
- **Caching**: Task plans and results can be cached for similar requests
- **Parallel Execution**: Independent tasks can be executed in parallel (future enhancement)

## Limitations

- Requires Pydantic AI dependency
- Additional model costs for task decomposition
- May over-decompose simple requests
- Human approval can slow down execution
- Limited to aider's existing tool capabilities

## Future Enhancements

- **Parallel Task Execution**: Execute independent tasks simultaneously
- **Learning from History**: Improve decomposition based on past successes
- **Custom Agent Tools**: Allow users to define custom tools for agents
- **Visual Progress Tracking**: Web-based dashboard for task monitoring
- **Integration with External Tools**: Connect to project management systems

## Dependencies

- `pydantic-ai`: Core agentic framework
- `pydantic`: Data validation and serialization
- `asyncio`: Asynchronous execution support

## Installation

The agentic features are included with aider but require the pydantic-ai dependency:

```bash
pip install pydantic-ai
```

Or install aider with agentic support:

```bash
pip install aider-chat[agentic]
```

## Contributing

To contribute to the agentic integration:

1. Understand the existing aider architecture
2. Follow the Pydantic AI patterns for agent development
3. Maintain backward compatibility with existing workflows
4. Add comprehensive tests for new functionality
5. Update documentation and examples

## Support

For issues related to agentic functionality:

1. Check that pydantic-ai is properly installed
2. Verify model compatibility with Pydantic AI
3. Review task decomposition quality
4. Check for dependency conflicts

Report bugs and feature requests in the main aider repository with the "agentic" label.
