"""
Progress monitoring and human-in-the-loop controls for agentic workflows.
"""
import time
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .task_manager import TaskManager, TaskStatus


class InterventionType(Enum):
    """Types of human intervention."""
    APPROVAL_REQUIRED = "approval_required"
    TASK_FAILED = "task_failed"
    PROGRESS_UPDATE = "progress_update"
    PLAN_MODIFICATION = "plan_modification"


@dataclass
class InterventionRequest:
    """Request for human intervention."""
    type: InterventionType
    message: str
    options: Dict[str, str]
    default_option: Optional[str] = None
    task_id: Optional[str] = None


class ProgressMonitor:
    """Monitors task progress and handles human-in-the-loop interactions."""
    
    def __init__(self, task_manager: TaskManager, io=None):
        self.task_manager = task_manager
        self.io = io
        self.start_time = None
        self.intervention_handlers: Dict[InterventionType, Callable] = {}
        self.progress_callbacks: list = []
        
    def start_monitoring(self):
        """Start monitoring progress."""
        self.start_time = time.time()
        
    def stop_monitoring(self):
        """Stop monitoring progress."""
        self.start_time = None
        
    def get_elapsed_time(self) -> float:
        """Get elapsed time since monitoring started."""
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time
        
    def register_intervention_handler(self, intervention_type: InterventionType, handler: Callable):
        """Register a handler for a specific intervention type."""
        self.intervention_handlers[intervention_type] = handler
        
    def register_progress_callback(self, callback: Callable):
        """Register a callback for progress updates."""
        self.progress_callbacks.append(callback)
        
    def request_intervention(self, request: InterventionRequest) -> str:
        """Request human intervention."""
        # Use registered handler if available
        if request.type in self.intervention_handlers:
            return self.intervention_handlers[request.type](request)
            
        # Default handling using IO
        if self.io:
            return self._default_intervention_handler(request)
            
        # Fallback to default option
        return request.default_option or list(request.options.keys())[0]
        
    def _default_intervention_handler(self, request: InterventionRequest) -> str:
        """Default intervention handler using IO."""
        self.io.tool_output(f"\n🤔 {request.message}")
        
        if len(request.options) == 1:
            # Single option - just confirm
            option_key = list(request.options.keys())[0]
            response = self.io.prompt_ask(
                f"{request.options[option_key]} (y/n):",
                default="y" if request.default_option == option_key else "n"
            ).lower().strip()
            return option_key if response in ['y', 'yes'] else 'cancel'
        else:
            # Multiple options
            options_text = "\n".join([f"  {key}: {desc}" for key, desc in request.options.items()])
            self.io.tool_output(f"Options:\n{options_text}")
            
            response = self.io.prompt_ask(
                f"Choose option ({'/'.join(request.options.keys())}):",
                default=request.default_option or list(request.options.keys())[0]
            ).lower().strip()
            
            return response if response in request.options else request.default_option
            
    def update_progress(self):
        """Update progress and notify callbacks."""
        progress = self.task_manager.get_progress_summary()
        elapsed = self.get_elapsed_time()
        
        progress_info = {
            **progress,
            "elapsed_time": elapsed,
            "estimated_remaining": self._estimate_remaining_time(progress, elapsed)
        }
        
        # Notify callbacks
        for callback in self.progress_callbacks:
            try:
                callback(progress_info)
            except Exception as e:
                if self.io:
                    self.io.tool_error(f"Progress callback error: {str(e)}")
                    
        return progress_info
        
    def _estimate_remaining_time(self, progress: Dict[str, Any], elapsed: float) -> Optional[float]:
        """Estimate remaining time based on current progress."""
        if progress['completed'] == 0:
            return None
            
        avg_time_per_task = elapsed / progress['completed']
        remaining_tasks = progress['total'] - progress['completed']
        
        return avg_time_per_task * remaining_tasks
        
    def display_progress_bar(self, progress: Optional[Dict[str, Any]] = None):
        """Display a progress bar."""
        if not self.io:
            return
            
        if progress is None:
            progress = self.task_manager.get_progress_summary()
            
        total = progress['total']
        completed = progress['completed']
        failed = progress['failed']
        
        if total == 0:
            return
            
        # Create progress bar
        bar_width = 40
        completed_width = int((completed / total) * bar_width)
        failed_width = int((failed / total) * bar_width)
        remaining_width = bar_width - completed_width - failed_width
        
        bar = "█" * completed_width + "▓" * failed_width + "░" * remaining_width
        percentage = (completed / total) * 100
        
        elapsed = self.get_elapsed_time()
        elapsed_str = f"{elapsed:.1f}s" if elapsed < 60 else f"{elapsed/60:.1f}m"
        
        self.io.tool_output(
            f"Progress: [{bar}] {completed}/{total} ({percentage:.1f}%) "
            f"- {failed} failed - {elapsed_str} elapsed"
        )
        
    def handle_task_failure(self, task_id: str, error: str) -> str:
        """Handle task failure with human intervention."""
        task = self.task_manager.get_task(task_id)
        if not task:
            return "skip"
            
        request = InterventionRequest(
            type=InterventionType.TASK_FAILED,
            message=f"Task '{task.title}' failed: {error}",
            options={
                "retry": "Retry the task",
                "skip": "Skip this task and continue",
                "modify": "Modify the task and retry",
                "abort": "Abort the entire workflow"
            },
            default_option="skip",
            task_id=task_id
        )
        
        return self.request_intervention(request)
        
    def request_task_approval(self, task_id: str) -> bool:
        """Request approval to execute a task."""
        task = self.task_manager.get_task(task_id)
        if not task:
            return False
            
        request = InterventionRequest(
            type=InterventionType.APPROVAL_REQUIRED,
            message=f"Execute task: {task.title}?",
            options={
                "yes": "Execute the task",
                "no": "Skip this task",
                "details": "Show task details first"
            },
            default_option="yes",
            task_id=task_id
        )
        
        response = self.request_intervention(request)
        
        if response == "details":
            if self.io:
                self.io.tool_output(f"\nTask Details:")
                self.io.tool_output(f"Title: {task.title}")
                self.io.tool_output(f"Description: {task.description}")
                self.io.tool_output(f"Priority: {task.priority}")
                if task.dependencies:
                    self.io.tool_output(f"Dependencies: {', '.join(task.dependencies)}")
                    
            # Ask again after showing details
            return self.request_task_approval(task_id)
            
        return response == "yes"
        
    def request_plan_modification(self) -> Dict[str, Any]:
        """Request plan modification from user."""
        request = InterventionRequest(
            type=InterventionType.PLAN_MODIFICATION,
            message="How would you like to modify the task plan?",
            options={
                "add": "Add new tasks",
                "remove": "Remove tasks", 
                "reorder": "Reorder tasks",
                "modify": "Modify existing tasks",
                "cancel": "Cancel modifications"
            },
            default_option="cancel"
        )
        
        response = self.request_intervention(request)
        
        # TODO: Implement specific modification handlers
        return {"action": response, "details": {}}
        
    def get_status_summary(self) -> str:
        """Get a formatted status summary."""
        progress = self.task_manager.get_progress_summary()
        elapsed = self.get_elapsed_time()
        
        summary_lines = [
            f"📊 Task Status Summary",
            f"Total tasks: {progress['total']}",
            f"✅ Completed: {progress['completed']}",
            f"❌ Failed: {progress['failed']}",
            f"⏳ Pending: {progress['pending']}",
            f"🔄 In Progress: {progress['in_progress']}",
            f"⏱️ Elapsed: {elapsed:.1f}s",
            f"📈 Progress: {progress['progress_percent']:.1f}%"
        ]
        
        if progress['completed'] > 0:
            estimated_remaining = self._estimate_remaining_time(progress, elapsed)
            if estimated_remaining:
                summary_lines.append(f"🔮 Est. Remaining: {estimated_remaining:.1f}s")
                
        return "\n".join(summary_lines)
