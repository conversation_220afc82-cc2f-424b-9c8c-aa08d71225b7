from .base_coder import Coder
from aider.agentic.task_manager import Task<PERSON><PERSON><PERSON>, Task, TaskStatus
from aider.agentic.agents import AgentContext

class AgenticCoder(Coder):
    """
    A specialized coder that adds agentic task decomposition and execution.
    
    This coder breaks down complex requests into manageable tasks and executes them
    with human-in-the-loop control.
    """
    edit_format = "agentic"
    
    def __init__(self, main_model, io, **kwargs):
        """Initialize the AgenticCoder with agentic capabilities."""
        # Extract agentic-specific parameters
        self.agentic_model = kwargs.pop('agentic_model', None)
        self.auto_approve = kwargs.pop('auto_approve', False)

        # Initialize the base coder
        super().__init__(main_model, io, **kwargs)
        
        # Initialize agentic components
        self.task_manager = TaskManager()
        self.agent_context = AgentContext(
            coder=self,
            io=self.io,
            repo=self.repo
        )
        self.agent_context.task_manager = self.task_manager
        
        # Track task creation state to prevent multiple task creation loops
        self._tasks_created_for_session = False
        self._current_session_message = None
        self._execution_in_progress = False
        
        # Load agentic prompt template
        self.agentic_prompt_template = self._load_agentic_prompt_template()
    
    def _load_agentic_prompt_template(self):
        """Load the agentic prompt template from file."""
        try:
            import os
            # Try to find the template file relative to the repo root
            template_path = None
            if hasattr(self, 'repo') and self.repo:
                repo_root = self.repo.root
                template_path = os.path.join(repo_root, 'agentic_prompt_template.md')

            # If not found in repo, try current directory
            if not template_path or not os.path.exists(template_path):
                template_path = 'agentic_prompt_template.md'

            # If still not found, try relative to this file
            if not os.path.exists(template_path):
                current_dir = os.path.dirname(os.path.abspath(__file__))
                template_path = os.path.join(current_dir, '..', '..', 'agentic_prompt_template.md')

            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                self.io.tool_warning("Agentic prompt template not found, using default behavior")
                return ""
        except Exception as e:
            self.io.tool_warning(f"Error loading agentic prompt template: {e}")
            return ""
    
    def run(self, with_message=None, preproc=True):
        """
        Run the agentic workflow for a user message.
        
        This method:
        1. Decomposes the request into tasks
        2. Gets human approval for the task plan
        3. Executes tasks with optional human-in-the-loop control
        4. Returns the final result
        """
        if not with_message:
            # Get input if not provided
            if not self.io.placeholder:
                self.copy_context()
            with_message = self.get_input()
        else:
            self.io.user_input(with_message)
        
        # Run the agentic workflow
        return self.run_agentic(with_message)
    
    def run_agentic(self, user_message):
        """Run the agentic workflow for a user message."""
        # Prevent task creation if execution is already in progress
        if self._execution_in_progress:
            self.io.tool_output("🔄 Task execution already in progress, skipping task creation...")
            return None

        try:
            # Check if tasks have already been created for this session/message
            if self._tasks_created_for_session and self._current_session_message == user_message:
                # Tasks already exist for this message, just execute them
                self.io.tool_output("🔄 Continuing with existing task plan...")
                return self._execute_task_plan()

            # Check if we have tasks but for a different message - clear them
            if self.task_manager.tasks and self._current_session_message != user_message:
                self.io.tool_output("🔄 New request detected, clearing previous tasks...")
                self.task_manager.clear()
                self._tasks_created_for_session = False

            # If we already have tasks for this exact message, don't recreate them
            if self.task_manager.tasks and self._current_session_message == user_message:
                self.io.tool_output("🔄 Using existing task plan for this request...")
                return self._execute_task_plan()

            # Set current session message
            self._current_session_message = user_message

            # Step 1: Decompose the request into tasks
            self.io.tool_output("🤖 Analyzing request and creating task plan...")
            decomposition_result = self._run_sync_decomposition(user_message)

            if not decomposition_result or not decomposition_result.tasks:
                self.io.tool_error("Failed to decompose the request into tasks.")
                return None

            # Step 2: Create tasks in the task manager (only the intelligent tasks)
            task_ids = []
            title_to_id_map = {}

            # Filter out any generic fallback tasks to prevent duplication
            filtered_tasks = []
            for task_data in decomposition_result.tasks:
                title = task_data.get('title', '')
                # Skip generic fallback tasks that might cause duplication
                if not any(generic in title for generic in ['(Generic)', 'Analyze Requirements', 'Implement Solution', 'Create Tests']):
                    filtered_tasks.append(task_data)

            # First pass: create all tasks without dependencies
            for task_data in filtered_tasks:
                task_id = self.task_manager.create_task(
                    title=task_data.get('title', ''),
                    description=task_data.get('description', ''),
                    priority=task_data.get('priority', 2),
                    dependencies=[],  # Will be set in second pass
                    metadata=task_data.get('metadata', {})
                )
                task_ids.append(task_id)
                title_to_id_map[task_data.get('title', '')] = task_id

            # Second pass: update dependencies using task IDs
            for i, task_data in enumerate(filtered_tasks):
                task_id = task_ids[i]
                task = self.task_manager.get_task(task_id)
                if task:
                    # Convert dependency titles to task IDs
                    dependency_ids = []
                    for dep_title in task_data.get('dependencies', []):
                        if dep_title in title_to_id_map:
                            dependency_ids.append(title_to_id_map[dep_title])
                    task.dependencies = dependency_ids

            # Mark that tasks have been created for this session
            self._tasks_created_for_session = True

            # Step 3: Show task plan to user
            self._display_task_plan(decomposition_result.reasoning)

            if not self.auto_approve:
                if not self._get_user_approval():
                    self.io.tool_output("Task execution cancelled by user.")
                    # Clear tasks when user cancels to prevent duplication on restart
                    self.task_manager.clear()
                    self._tasks_created_for_session = False
                    self._execution_in_progress = False
                    return None

            # Step 4: Execute tasks
            self._execution_in_progress = True
            try:
                return self._execute_task_plan()
            finally:
                self._execution_in_progress = False

        except Exception as e:
            self.io.tool_error(f"Error in agentic workflow: {str(e)}")
            # Fall back to normal coder behavior
            return super().run_one(user_message)

    def _display_task_plan(self, reasoning):
        """Display the task plan to the user."""
        self.io.tool_output("\n📋 Task Plan:")
        self.io.tool_output(f"Reasoning: {reasoning}")
        self.io.tool_output("\nTasks to execute:")

        for i, task in enumerate(self.task_manager.tasks.values(), 1):
            deps_str = f" (depends on: {', '.join(task.dependencies)})" if task.dependencies else ""

            self.io.tool_output(
                f"[ ] {task.title}{deps_str}\n"
                f"    {task.description}"
            )

    def _get_user_approval(self):
        """Get user approval for the task plan."""
        response = self.io.prompt_ask(
            "\nDo you want to proceed with this task plan? (y/n/m for modify):",
            default="y"
        ).lower().strip()

        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            # Clear tasks when user says no to prevent duplication
            self.task_manager.clear()
            return False
        elif response in ['m', 'modify']:
            # TODO: Implement task plan modification
            self.io.tool_output("Task plan modification not yet implemented.")
            # Clear tasks when user wants to modify
            self.task_manager.clear()
            return False
        else:
            # Default to no for any other input
            self.task_manager.clear()
            return False

    def _execute_task_plan(self):
        """Execute the task plan with automatic progression through tasks."""
        results = []

        while not self.task_manager.is_complete():
            # Get next ready task
            next_task = self.task_manager.get_next_task()
            if not next_task:
                # No ready tasks - check if we're stuck
                pending_tasks = self.task_manager.get_tasks_by_status(TaskStatus.PENDING)
                if pending_tasks:
                    self.io.tool_error("No tasks ready to execute. Possible dependency cycle.")
                    break
                else:
                    break

            # Display progress
            progress = self.task_manager.get_progress_summary()
            self.io.tool_output(
                f"\n⚡ Executing task: {next_task.title} "
                f"({progress['completed']}/{progress['total']} completed)"
            )

            # Execute the task automatically (no human approval for each task)
            try:
                self.task_manager.update_task_status(next_task.id, TaskStatus.IN_PROGRESS)

                execution_result = self._run_sync_execution(next_task)

                if execution_result.success:
                    self.task_manager.update_task_status(
                        next_task.id, TaskStatus.COMPLETED, result=execution_result.result
                    )
                    self.io.tool_output(f"✅ Task completed: {execution_result.result}")
                    results.append(execution_result.result)

                    # Continue automatically to next task
                    continue
                else:
                    # Task failed, but we'll mark it as completed with error note
                    # This allows dependent tasks to still run if they can proceed
                    self.task_manager.update_task_status(
                        next_task.id, TaskStatus.COMPLETED,
                        result=f"Task completed with issues: {execution_result.error}",
                        error=execution_result.error
                    )
                    self.io.tool_warning(f"⚠️ Task completed with issues: {execution_result.error}")
                    results.append(f"Task '{next_task.title}' completed with issues: {execution_result.error}")

                    # Continue with remaining tasks
                    continue

            except Exception as e:
                # Mark as completed with error to allow dependent tasks to proceed
                self.task_manager.update_task_status(
                    next_task.id, TaskStatus.COMPLETED,
                    result=f"Task completed with exception: {str(e)}",
                    error=str(e)
                )
                self.io.tool_warning(f"⚠️ Task execution error: {str(e)}")
                results.append(f"Task '{next_task.title}' completed with exception: {str(e)}")
                # Continue with remaining tasks
                continue

        # Display final summary
        self._display_execution_summary()

        return "\n".join(results) if results else None

    def _display_execution_summary(self):
        """Display a summary of task execution."""
        progress = self.task_manager.get_progress_summary()

        self.io.tool_output("\n📊 Execution Summary:")
        self.io.tool_output(f"Total tasks: {progress['total']}")
        self.io.tool_output(f"Completed: {progress['completed']}")
        self.io.tool_output(f"Failed: {progress['failed']}")
        self.io.tool_output(f"Progress: {progress['progress_percent']:.1f}%")

    def _run_sync_decomposition(self, user_message):
        """Run intelligent task decomposition using the architect coder."""

        try:
            # Use architect coder for intelligent task decomposition
            self.io.tool_output("🧠 Using architect coder to analyze request and create intelligent task breakdown...")

            # Create architect coder for task planning
            architect_result = self._use_architect_for_task_planning(user_message)

            if architect_result and architect_result.get('tasks') and len(architect_result['tasks']) > 0:
                tasks = architect_result['tasks']
                reasoning = architect_result.get('reasoning', f"Architect analyzed the request '{user_message}' and created {len(tasks)} specific tasks.")
                self.io.tool_output(f"✅ Successfully created {len(tasks)} intelligent tasks")
            else:
                # Fallback to generic tasks if architect fails
                self.io.tool_warning("Architect task planning failed, using fallback task creation")
                tasks = self._create_fallback_tasks(user_message)
                reasoning = f"Used fallback task creation for '{user_message}' due to architect planning failure."

        except Exception as e:
            # Fallback on any error
            self.io.tool_error(f"Task decomposition error: {str(e)}")
            tasks = self._create_fallback_tasks(user_message)
            reasoning = f"Used fallback task creation for '{user_message}' due to error: {str(e)}"

        # Create a mock result object
        class MockDecompositionResult:
            def __init__(self, tasks, reasoning):
                self.tasks = tasks
                self.reasoning = reasoning

        return MockDecompositionResult(tasks, reasoning)

    def _use_architect_for_task_planning(self, user_message):
        """Use a simplified approach to create intelligent task breakdown."""

        try:
            # Create a detailed prompt for task decomposition using the base coder
            task_planning_prompt = f"""
Analyze this coding request and break it down into a logical sequence of specific, actionable tasks:

USER REQUEST: "{user_message}"

Create a detailed task breakdown with:
1. Clear, specific task titles
2. Detailed descriptions of work required
3. Logical dependencies between tasks
4. Files that need to be examined or created
5. Testing requirements

Format as a numbered list of tasks with clear descriptions.
Be specific about what each task accomplishes and how they build on each other.
Focus on creating a practical, executable plan.
"""

            # Use base coder to analyze the request and capture the response
            result = super().run_one(task_planning_prompt, preproc=True)

            # Get the response from the partial_response_content
            response = getattr(self, 'partial_response_content', None)

            # Parse the response into tasks
            if response:
                return self._parse_architect_response(response, user_message)
            else:
                return None

        except Exception as e:
            self.io.tool_error(f"Error using architect for task planning: {str(e)}")
            return None

    def _parse_architect_response(self, architect_response, user_message):
        """Parse the response into structured tasks."""
        try:
            # Extract tasks from the response
            tasks = []

            # Split response into lines and look for numbered tasks
            lines = architect_response.split('\n')
            current_task = None
            task_counter = 1

            import re
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for numbered tasks with multiple formats:
                # "1. **Task Title: Define Calculator Class**", " 1 Task Title", etc.
                task_match = re.match(r'^\s*(\d+)\.?\s*\*?\*?(?:Task Title:\s*)?(.+?)\*?\*?$', line)
                if task_match:
                    task_num = int(task_match.group(1))
                    if task_num == task_counter:
                        # Save previous task if exists
                        if current_task:
                            tasks.append(current_task)

                        # Start new task
                        task_title = task_match.group(2).strip()
                        # Remove any markdown formatting
                        task_title = task_title.replace("**", "").replace("*", "")

                        current_task = {
                            'title': task_title[:80],  # Limit title length
                            'description': f'Execute: {task_title}',
                            'priority': 1,
                            'dependencies': [],
                            'metadata': {'type': 'intelligent_planned', 'order': task_counter}
                        }
                        task_counter += 1

                elif current_task and line and not re.match(r'^\s*\d+\.?\s+', line):
                    # Add to current task description if it's not too long and not a new task
                    if (len(current_task['description']) < 300 and
                        not line.startswith('•') and
                        not line.startswith('Description:') and
                        not line.startswith('Dependencies:') and
                        not line.startswith('Files:')):
                        current_task['description'] += f" {line}"

            # Add the last task
            if current_task:
                tasks.append(current_task)

            # If we didn't find structured tasks, create intelligent fallback based on keywords
            if not tasks:
                tasks = self._create_intelligent_fallback_tasks(user_message, architect_response)

            # Set up simple sequential dependencies
            for i in range(1, len(tasks)):
                tasks[i]['dependencies'] = [tasks[i-1]['title']]

            return {
                'tasks': tasks,
                'reasoning': f"Intelligently analyzed '{user_message}' and created {len(tasks)} specific tasks based on detailed analysis."
            }

        except Exception as e:
            self.io.tool_error(f"Error parsing response: {str(e)}")
            # Return simple fallback
            return {
                'tasks': self._create_fallback_tasks(user_message),
                'reasoning': f"Used fallback task creation for '{user_message}' due to parsing error."
            }

    def _create_intelligent_fallback_tasks(self, user_message, response):
        """Create intelligent fallback tasks based on the response content."""
        # Extract key concepts from the response
        response_lower = response.lower()

        # Look for common development patterns
        if any(word in response_lower for word in ['class', 'function', 'method', 'implement']):
            if any(word in response_lower for word in ['test', 'testing', 'verify']):
                return [
                    {
                        'title': 'Analyze and Implement with Tests',
                        'description': f'Analyze the requirements and implement with comprehensive testing for: {user_message}',
                        'priority': 1,
                        'dependencies': [],
                        'metadata': {'type': 'comprehensive_with_tests'}
                    }
                ]
            else:
                return [
                    {
                        'title': 'Design and Implement Solution',
                        'description': f'Design and implement the complete solution for: {user_message}',
                        'priority': 1,
                        'dependencies': [],
                        'metadata': {'type': 'comprehensive_implementation'}
                    }
                ]

        # Default to simple comprehensive implementation
        return [
            {
                'title': 'Complete Implementation',
                'description': f'Understand requirements and implement complete solution for: {user_message}',
                'priority': 1,
                'dependencies': [],
                'metadata': {'type': 'comprehensive'}
            }
        ]

    def _create_fallback_tasks(self, user_message):
        """Create fallback tasks when intelligent decomposition fails."""
        # Simple but more intelligent fallback based on keywords
        message_lower = user_message.lower()

        if any(word in message_lower for word in ['update', 'modify', 'change', 'fix', 'improve']):
            return [
                {
                    'title': 'Analyze Current Code (Generic)',
                    'description': f'Examine the existing codebase to understand what needs to be updated for: {user_message}',
                    'priority': 1,
                    'dependencies': [],
                    'metadata': {'type': 'analysis'}
                }
            ]
        elif any(word in message_lower for word in ['create', 'build', 'add', 'new']):
            return [
                {
                    'title': 'Design and Plan (Generic)',
                    'description': f'Design the architecture and plan the implementation for: {user_message}',
                    'priority': 1,
                    'dependencies': [],
                    'metadata': {'type': 'design'}
                }
            ]
        else:
            # Generic analysis and implementation
            return [
                {
                    'title': 'Understand Requirements(Generic)',
                    'description': f'Analyze and understand the requirements: {user_message}',
                    'priority': 1,
                    'dependencies': [],
                    'metadata': {'type': 'analysis'}
                }
            ]

    def _run_sync_execution(self, task):
        """Run task execution with agentic prompt template and iteration capabilities."""
        try:
            # Create a detailed execution prompt with agentic template
            task_type = task.metadata.get('type', 'general')
            files_to_examine = task.metadata.get('files_to_examine', [])

            # Build the execution prompt using the agentic template
            execution_prompt = self._build_agentic_execution_prompt(task, task_type, files_to_examine)

            # Execute with iteration capabilities
            return self._execute_with_iteration(task, execution_prompt, task_type)

        except Exception as e:
            class MockExecutionResult:
                def __init__(self, success, result=None, error=None, reasoning=""):
                    self.success = success
                    self.result = result
                    self.error = error
                    self.reasoning = reasoning

            return MockExecutionResult(
                success=False,
                error=str(e),
                reasoning=f"Failed to execute task: {str(e)}"
            )

    def _build_agentic_execution_prompt(self, task, task_type, files_to_examine):
        """Build execution prompt using the agentic template."""
        # Base task information
        task_info = f"""
TASK EXECUTION REQUEST:

Task: {task.title}
Description: {task.description}
Type: {task_type}

{f"FILES TO EXAMINE: {', '.join(files_to_examine)}" if files_to_examine else ""}
"""

        # Add agentic prompt template if available
        if self.agentic_prompt_template:
            return f"{self.agentic_prompt_template}\n\n{task_info}\n\nPlease execute this task following the agentic workflow above. Make sure to iterate until the task is completely solved. IMPORTANT: Avoid repeating the same todo list or status summaries multiple times - focus on execution and provide concise updates."
        else:
            # Fallback prompt with agentic principles
            return f"""{task_info}

AGENTIC EXECUTION INSTRUCTIONS:
- You MUST iterate and keep going until the task is completely solved
- Make small, testable, incremental changes
- Test frequently and debug as needed
- If you encounter errors, fix them before proceeding
- Only consider the task complete when all requirements are met and tests pass
- Provide a clear summary of what was accomplished
- IMPORTANT: Avoid repeating todo lists or status summaries - focus on execution

Please execute this task thoroughly and iterate until it's completely solved.
"""

    def _execute_with_iteration(self, task, execution_prompt, task_type):
        """Execute task with iteration capabilities to fix errors."""
        max_iterations = 3  # Prevent infinite loops
        iteration = 0
        last_result = None

        class MockExecutionResult:
            def __init__(self, success, result=None, error=None, reasoning=""):
                self.success = success
                self.result = result
                self.error = error
                self.reasoning = reasoning

        while iteration < max_iterations:
            iteration += 1

            try:
                # Add iteration context if this is a retry
                current_prompt = execution_prompt
                if iteration > 1 and last_result:
                    current_prompt += f"\n\nPREVIOUS ATTEMPT RESULT (Iteration {iteration-1}):\n{last_result}\n\nPlease analyze the previous result and improve upon it. Fix any issues and complete the task. IMPORTANT: Do not repeat todo lists or status summaries - focus on the specific improvements needed."

                # Use the base coder to process the task
                result = super().run_one(current_prompt, preproc=True)
                last_result = result

                # Check if the result indicates success or if we should iterate
                if result and self._is_task_successful(result, task):
                    result_summary = self._extract_task_summary(result, task)
                    return MockExecutionResult(
                        success=True,
                        result=f"{result_summary} (Completed in {iteration} iteration{'s' if iteration > 1 else ''})",
                        reasoning=f"Successfully executed '{task.title}' - {task_type} task completed after {iteration} iteration{'s' if iteration > 1 else ''}"
                    )
                elif iteration == max_iterations:
                    # Max iterations reached
                    result_summary = self._extract_task_summary(result or "Task execution incomplete", task)
                    return MockExecutionResult(
                        success=True,  # Still consider it successful, but note the iterations
                        result=f"{result_summary} (Completed after {max_iterations} iterations - may need review)",
                        reasoning=f"Task '{task.title}' completed after maximum iterations"
                    )

            except Exception as e:
                if iteration == max_iterations:
                    return MockExecutionResult(
                        success=False,
                        error=str(e),
                        reasoning=f"Failed to execute task after {max_iterations} iterations: {str(e)}"
                    )
                # Continue to next iteration on error
                last_result = f"Error in iteration {iteration}: {str(e)}"

        # Should not reach here, but just in case
        return MockExecutionResult(
            success=False,
            error="Maximum iterations exceeded",
            reasoning="Task execution failed after maximum iterations"
        )

    def _is_task_successful(self, result, task):
        """Check if the task execution result indicates success."""
        if not result:
            return False

        # Look for success indicators
        success_indicators = [
            'completed', 'successful', 'done', 'finished', 'implemented',
            'created', 'updated', 'fixed', 'resolved', 'working'
        ]

        # Look for failure indicators
        failure_indicators = [
            'error', 'failed', 'exception', 'traceback', 'not working',
            'issue', 'problem', 'unable to', 'cannot', 'could not'
        ]

        result_lower = result.lower()

        # Count indicators
        success_count = sum(1 for indicator in success_indicators if indicator in result_lower)
        failure_count = sum(1 for indicator in failure_indicators if indicator in result_lower)

        # If we have more success indicators than failure indicators, consider it successful
        # Also consider it successful if we have success indicators and no failure indicators
        return success_count > failure_count or (success_count > 0 and failure_count == 0)

    def _extract_task_summary(self, result, task):
        """Extract a concise summary from the task execution result."""
        if not result:
            return f"Task '{task.title}' completed"

        # Try to extract key information from the result
        lines = result.split('\n')
        summary_lines = []

        # Look for key indicators of what was accomplished
        for line in lines:
            line = line.strip()
            if any(indicator in line.lower() for indicator in [
                'created', 'modified', 'updated', 'added', 'removed', 'fixed',
                'implemented', 'analyzed', 'found', 'identified', 'completed'
            ]):
                summary_lines.append(line)
                if len(summary_lines) >= 3:  # Limit to top 3 accomplishments
                    break

        if summary_lines:
            summary = f"Task '{task.title}' completed:\n" + '\n'.join(f"• {line}" for line in summary_lines)
        else:
            # Fallback to first few lines if no specific indicators found
            first_lines = [line.strip() for line in lines[:2] if line.strip()]
            if first_lines:
                summary = f"Task '{task.title}' completed:\n" + '\n'.join(f"• {line}" for line in first_lines)
            else:
                summary = f"Task '{task.title}' completed successfully"

        return summary
