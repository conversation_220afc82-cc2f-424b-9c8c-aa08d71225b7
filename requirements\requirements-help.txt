# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements/requirements-help.txt requirements/requirements-help.in
aiohappyeyeballs==2.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
aiohttp==3.11.18
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   llama-index-core
aiosignal==1.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
annotated-types==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
anyio==4.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   httpx
attrs==25.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
banks==2.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
certifi==2025.4.26
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements/common-constraints.txt
    #   nltk
colorama==0.4.6
    # via
    #   -c requirements/common-constraints.txt
    #   griffe
dataclasses-json==0.6.7
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
deprecated==1.2.18
    # via
    #   -c requirements/common-constraints.txt
    #   banks
    #   llama-index-core
dirtyjson==1.0.8
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
filelock==3.18.0
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   torch
    #   transformers
filetype==1.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
frozenlist==1.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   llama-index-core
    #   torch
greenlet==3.2.2
    # via
    #   -c requirements/common-constraints.txt
    #   sqlalchemy
griffe==1.7.3
    # via
    #   -c requirements/common-constraints.txt
    #   banks
h11==0.16.0
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
hf-xet==1.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
httpcore==1.0.9
    # via
    #   -c requirements/common-constraints.txt
    #   httpx
httpx==0.28.1
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
huggingface-hub[inference]==0.31.1
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-embeddings-huggingface
    #   sentence-transformers
    #   tokenizers
    #   transformers
idna==3.10
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   httpx
    #   requests
    #   yarl
jinja2==3.1.6
    # via
    #   -c requirements/common-constraints.txt
    #   banks
    #   torch
joblib==1.5.0
    # via
    #   -c requirements/common-constraints.txt
    #   nltk
    #   scikit-learn
llama-index-core==0.12.26
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-help.in
    #   llama-index-embeddings-huggingface
llama-index-embeddings-huggingface==0.5.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-help.in
markupsafe==3.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   jinja2
marshmallow==3.26.1
    # via
    #   -c requirements/common-constraints.txt
    #   dataclasses-json
mpmath==1.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   sympy
multidict==6.4.3
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   typing-inspect
nest-asyncio==1.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
networkx==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
    #   torch
nltk==3.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
numpy==1.26.4
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-help.in
    #   llama-index-core
    #   scikit-learn
    #   scipy
    #   transformers
packaging==24.2
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   marshmallow
    #   transformers
pillow==11.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
    #   sentence-transformers
platformdirs==4.3.8
    # via
    #   -c requirements/common-constraints.txt
    #   banks
propcache==0.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
pydantic==2.11.4
    # via
    #   -c requirements/common-constraints.txt
    #   banks
    #   llama-index-core
pydantic-core==2.33.2
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
pyyaml==6.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   llama-index-core
    #   transformers
regex==2024.11.6
    # via
    #   -c requirements/common-constraints.txt
    #   nltk
    #   tiktoken
    #   transformers
requests==2.32.3
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   llama-index-core
    #   tiktoken
    #   transformers
safetensors==0.5.3
    # via
    #   -c requirements/common-constraints.txt
    #   transformers
scikit-learn==1.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   sentence-transformers
scipy==1.15.3
    # via
    #   -c requirements/common-constraints.txt
    #   scikit-learn
    #   sentence-transformers
sentence-transformers==4.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-embeddings-huggingface
sniffio==1.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
sqlalchemy[asyncio]==2.0.40
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
sympy==1.14.0
    # via
    #   -c requirements/common-constraints.txt
    #   torch
tenacity==9.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
threadpoolctl==3.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   scikit-learn
tiktoken==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   llama-index-core
tokenizers==0.21.1
    # via
    #   -c requirements/common-constraints.txt
    #   transformers
torch==2.2.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-help.in
    #   sentence-transformers
tqdm==4.67.1
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
    #   llama-index-core
    #   nltk
    #   sentence-transformers
    #   transformers
transformers==4.51.3
    # via
    #   -c requirements/common-constraints.txt
    #   sentence-transformers
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   huggingface-hub
    #   llama-index-core
    #   pydantic
    #   pydantic-core
    #   sentence-transformers
    #   sqlalchemy
    #   torch
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   dataclasses-json
    #   llama-index-core
typing-inspection==0.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
urllib3==2.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   requests
wrapt==1.17.2
    # via
    #   -c requirements/common-constraints.txt
    #   deprecated
    #   llama-index-core
yarl==1.20.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
