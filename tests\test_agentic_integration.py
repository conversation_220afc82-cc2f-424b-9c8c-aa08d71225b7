"""
Tests for agentic AI integration.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from aider.agentic.task_manager import TaskManager, Task, TaskStatus
from aider.agentic.agentic_coder import AgenticCoder
from aider.agentic.agents import AgentContext
from aider.io import InputOutput


class TestTaskManager:
    """Test the TaskManager class."""
    
    def test_create_task(self):
        """Test creating a task."""
        manager = TaskManager()
        task_id = manager.create_task(
            title="Test Task",
            description="A test task",
            priority=1
        )
        
        assert task_id in manager.tasks
        task = manager.get_task(task_id)
        assert task.title == "Test Task"
        assert task.description == "A test task"
        assert task.priority == 1
        assert task.status == TaskStatus.PENDING
        
    def test_task_dependencies(self):
        """Test task dependencies."""
        manager = TaskManager()
        
        # Create first task
        task1_id = manager.create_task("Task 1", "First task")
        
        # Create second task that depends on first
        task2_id = manager.create_task("Task 2", "Second task", dependencies=[task1_id])
        
        # Only task1 should be ready initially
        ready_tasks = manager.get_ready_tasks()
        assert len(ready_tasks) == 1
        assert ready_tasks[0].id == task1_id
        
        # Complete task1
        manager.update_task_status(task1_id, TaskStatus.COMPLETED)
        
        # Now task2 should be ready
        ready_tasks = manager.get_ready_tasks()
        assert len(ready_tasks) == 1
        assert ready_tasks[0].id == task2_id
        
    def test_progress_summary(self):
        """Test progress summary calculation."""
        manager = TaskManager()
        
        # Create some tasks
        task1_id = manager.create_task("Task 1", "First task")
        task2_id = manager.create_task("Task 2", "Second task")
        task3_id = manager.create_task("Task 3", "Third task")
        
        # Complete one task
        manager.update_task_status(task1_id, TaskStatus.COMPLETED)
        
        # Fail one task
        manager.update_task_status(task2_id, TaskStatus.FAILED)
        
        progress = manager.get_progress_summary()
        assert progress['total'] == 3
        assert progress['completed'] == 1
        assert progress['failed'] == 1
        assert progress['pending'] == 1
        assert progress['progress_percent'] == pytest.approx(33.33, rel=1e-2)
        
    def test_serialization(self):
        """Test task manager serialization."""
        manager = TaskManager()
        task_id = manager.create_task("Test Task", "A test task")
        
        # Serialize to dict
        data = manager.to_dict()
        
        # Create new manager from dict
        new_manager = TaskManager.from_dict(data)
        
        assert len(new_manager.tasks) == 1
        task = new_manager.get_task(task_id)
        assert task.title == "Test Task"
        assert task.description == "A test task"


class TestAgentContext:
    """Test the AgentContext class."""
    
    def test_agent_context_creation(self):
        """Test creating an agent context."""
        mock_coder = Mock()
        mock_io = Mock()
        mock_repo = Mock()
        
        context = AgentContext(coder=mock_coder, io=mock_io, repo=mock_repo)
        
        assert context.coder == mock_coder
        assert context.io == mock_io
        assert context.repo == mock_repo
        assert isinstance(context.task_manager, TaskManager)


class TestAgenticCoder:
    """Test the AgenticCoder class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_coder = Mock()
        self.mock_coder.io = Mock(spec=InputOutput)
        self.mock_coder.repo = Mock()
        self.mock_coder.root = "/test/root"
        
    def test_agentic_coder_creation(self):
        """Test creating an AgenticCoder."""
        # Mock the model to have a name and info
        self.mock_coder.main_model = Mock()
        self.mock_coder.main_model.name = "gpt-4"
        self.mock_coder.main_model.info = {"litellm_provider": "openai"}

        agentic_coder = AgenticCoder(self.mock_coder)

        assert agentic_coder.base_coder == self.mock_coder
        assert agentic_coder.agentic_enabled is True
        assert agentic_coder.human_approval_required is True
        assert isinstance(agentic_coder.task_manager, TaskManager)
            
    def test_agentic_coder_auto_approve(self):
        """Test AgenticCoder with auto-approve enabled."""
        # Mock the model to have a name and info
        self.mock_coder.main_model = Mock()
        self.mock_coder.main_model.name = "gpt-4"
        self.mock_coder.main_model.info = {"litellm_provider": "openai"}

        agentic_coder = AgenticCoder(self.mock_coder, auto_approve=True)

        assert agentic_coder.human_approval_required is False
            
    def test_fallback_to_normal_mode(self):
        """Test fallback to normal coder when agentic is disabled."""
        # Mock the model to have a name and info
        self.mock_coder.main_model = Mock()
        self.mock_coder.main_model.name = "gpt-4"
        self.mock_coder.main_model.info = {"litellm_provider": "openai"}

        agentic_coder = AgenticCoder(self.mock_coder)
        agentic_coder.enable_agentic_mode(False)

        # Mock the base coder's run method
        self.mock_coder.run.return_value = "Normal response"

        result = agentic_coder.run(with_message="Test message")

        # Should call the base coder's run method
        self.mock_coder.run.assert_called_once_with(with_message="Test message", preproc=True)
        assert result == "Normal response"
            
    def test_agentic_workflow_basic(self):
        """Test basic agentic workflow."""
        # Mock the model to have a name and info
        self.mock_coder.main_model = Mock()
        self.mock_coder.main_model.name = "gpt-4"
        self.mock_coder.main_model.info = {"litellm_provider": "openai"}

        agentic_coder = AgenticCoder(self.mock_coder, auto_approve=True)

        # Mock the synchronous helper methods
        def mock_decomposition(user_message):
            mock_result = Mock()
            mock_result.tasks = [
                {
                    'title': 'Test Task',
                    'description': 'A test task',
                    'priority': 1,
                    'dependencies': [],
                    'metadata': {}
                }
            ]
            mock_result.reasoning = "Test reasoning"
            return mock_result

        def mock_execution(task):
            mock_result = Mock()
            mock_result.success = True
            mock_result.result = "Task completed successfully"
            mock_result.error = None
            return mock_result

        agentic_coder._run_sync_decomposition = mock_decomposition
        agentic_coder._run_sync_execution = mock_execution

        # Run the agentic workflow
        result = agentic_coder.run_agentic("Create a test function")

        # Verify the workflow executed
        assert result == "Task completed successfully"
        assert len(agentic_coder.task_manager.tasks) == 1

        # Verify task was completed
        task = list(agentic_coder.task_manager.tasks.values())[0]
        assert task.status == TaskStatus.COMPLETED
        assert task.result == "Task completed successfully"


class TestIntegrationWithBaseCoder:
    """Test integration with the base Coder class."""
    
    def test_agentic_mode_parameter(self):
        """Test that agentic mode parameter is properly handled."""
        # This would require importing the actual Coder class
        # For now, we'll test the parameter passing
        
        # Mock the Coder class
        with patch('aider.coders.base_coder.Coder') as MockCoder:
            mock_instance = Mock()
            MockCoder.return_value = mock_instance
            
            # Test that agentic parameters are accepted
            mock_instance.agentic_mode = True
            mock_instance.agentic_model = None
            mock_instance.agentic_auto_approve = False
            
            assert mock_instance.agentic_mode is True
            assert mock_instance.agentic_auto_approve is False
            
    def test_get_agentic_coder_method(self):
        """Test the get_agentic_coder method."""
        mock_coder = Mock()
        mock_coder.agentic_mode = True
        mock_coder.agentic_model = None
        mock_coder.agentic_auto_approve = False
        mock_coder._agentic_coder = None
        mock_coder.io = Mock()
        
        # Mock the AgenticCoder import
        with patch('aider.agentic.AgenticCoder') as MockAgenticCoder:
            mock_agentic_instance = Mock()
            MockAgenticCoder.return_value = mock_agentic_instance
            
            # Simulate the get_agentic_coder method
            def get_agentic_coder():
                if not mock_coder.agentic_mode:
                    return None
                if mock_coder._agentic_coder is None:
                    auto_approve = getattr(mock_coder, 'agentic_auto_approve', False)
                    mock_coder._agentic_coder = MockAgenticCoder(
                        mock_coder, mock_coder.agentic_model, auto_approve=auto_approve
                    )
                return mock_coder._agentic_coder
            
            result = get_agentic_coder()
            
            assert result == mock_agentic_instance
            MockAgenticCoder.assert_called_once_with(
                mock_coder, mock_coder.agentic_model, auto_approve=False
            )


if __name__ == "__main__":
    pytest.main([__file__])
