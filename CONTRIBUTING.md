
# Contributing to the Project

We welcome contributions in the form of bug reports, feature requests,
and pull requests (PRs). This document describes how you can
contribute.

## Bug Reports and Feature Requests

Please submit bug reports and feature requests as GitHub issues. This
helps us to keep track of them and discuss potential solutions or
enhancements.

## LLM Benchmark Results

Contributions of
[LLM benchmark results](https://aider.chat/docs/leaderboards/)
are welcome!
See the
[benchmark README](https://github.com/Aider-AI/aider/blob/main/benchmark/README.md)
for information on running aider's code editing benchmarks.
Submit results by opening a PR with edits to the
[benchmark results data files](https://github.com/Aider-AI/aider/blob/main/aider/website/_data/).


## Pull Requests

We appreciate your pull requests. For small changes, feel free to
submit a PR directly. If you are considering a large or significant
change, please discuss it in a GitHub issue before submitting the
PR. This will save both you and the maintainers time, and it helps to
ensure that your contributions can be integrated smoothly.

## Licensing

Before contributing a PR, please review our
[Individual Contributor License Agreement](https://aider.chat/docs/legal/contributor-agreement.html).
All contributors will be asked to complete the agreement as part of the PR process.

## Setting up a Development Environment

### Clone the Repository

```
git clone https://github.com/Aider-AI/aider.git
cd aider
```

### Create a Virtual Environment

It is recommended to create a virtual environment outside of the repository to keep your development environment isolated.

#### Using `venv` (Python 3.9 and later)

```
python -m venv /path/to/venv
```

### Activate the Virtual Environment

#### On Windows

```
/path/to/venv/Scripts/activate
```

#### On Unix or macOS

```
source /path/to/venv/bin/activate
```

### Install the Project in Editable Mode

This step allows you to make changes to the source code and have them take effect immediately without reinstalling the package.

```
pip install -e .
```

### Install the Project Dependencies

```
pip install -r requirements.txt
```

For development, at least install the development dependencies:

```
pip install -r requirements/requirements-dev.txt
```

Consider installing other optional dependencies from the `requirements/` directory, if your development work needs them. 

Note that these dependency files are generated by `./scripts/pip-compile.sh` and then committed. See [Managing Dependencies](#managing-dependencies).

### Install Pre-commit Hooks (Optional)

The project uses pre-commit hooks for code formatting and linting. If you want to install and use these hooks, run:

```
pre-commit install
```

This will automatically run the pre-commit hooks when you commit changes to the repository.

Now you should have a fully functional development environment for the Aider project. You can start making changes, running tests, and contributing to the project.

### Handy Opinionated Setup Commands for MacOS / Linux

Here's an example of following the setup instructions above, for your copy/paste pleasure if your system works the same. Start in the project directory.

```
python3 -m venv ../aider_venv \
 && source ../aider_venv/bin/activate \
 && pip3 install -e . \
 && pip3 install -r requirements.txt \
 && pip3 install -r requirements/requirements-dev.txt
```

### Running Tests

Just run `pytest`.

### Building the Docker Image

The project includes a `Dockerfile` for building a Docker image. You can build the image by running:

```
docker build -t aider -f docker/Dockerfile .
```

### Building the Documentation

The project's documentation is built using Jekyll and hosted on GitHub Pages. To build the documentation locally, follow these steps:

1. Install Ruby and Bundler (if not already installed).
2. Navigate to the `aider/website` directory.
3. Install the required gems:
   ```
   bundle install
   ```
4. Build the documentation:
   ```
   bundle exec jekyll build
   ```
5. Preview the website while editing (optional):
   ```
   bundle exec jekyll serve
   ```

The built documentation will be available in the `aider/website/_site` directory.

## Coding Standards

### Python Compatibility

Aider supports Python versions 3.9, 3.10, 3.11, and 3.12. When contributing code, ensure compatibility with these supported Python versions.

### Code Style

The project follows the [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide for Python code, with a maximum line length of 100 characters. Additionally, the project uses [isort](https://pycqa.github.io/isort/) and [Black](https://black.readthedocs.io/en/stable/) for sorting imports and code formatting, respectively. Please install the pre-commit hooks to automatically format your code before committing changes.

### No Type Hints

The project does not use type hints.

### Testing

The project uses [pytest](https://docs.pytest.org/en/latest/) for running unit tests. The test files are located in the `aider/tests` directory and follow the naming convention `test_*.py`.

#### Running Tests

To run the entire test suite, use the following command from the project root directory:

```
pytest
```

You can also run specific test files or test cases by providing the file path or test name:

```
pytest tests/basic/test_coder.py
pytest tests/basic/test_coder.py::TestCoder::test_specific_case
```

#### Continuous Integration

The project uses GitHub Actions for continuous integration. The testing workflows are defined in the following files:

- `.github/workflows/ubuntu-tests.yml`: Runs tests on Ubuntu for Python versions 3.9 through 3.12.
- `.github/workflows/windows-tests.yml`: Runs that on Windows

These workflows are triggered on push and pull request events to the `main` branch, ignoring changes to the `aider/website/**` and `README.md` files.

#### Docker Build and Test

The `.github/workflows/docker-build-test.yml` workflow is used to build a Docker image for the project on every push or pull request event to the `main` branch. It checks out the code, sets up Docker, logs in to DockerHub, and then builds the Docker image without pushing it to the registry.

#### Writing Tests

When contributing new features or making changes to existing code, ensure that you write appropriate tests to maintain code coverage. Follow the existing patterns and naming conventions used in the `aider/tests` directory.

If you need to mock or create test data, consider adding it to the test files or creating separate fixtures or utility functions within the `aider/tests` directory.

#### Test Requirements

The project uses `pytest` as the testing framework, which is installed as a development dependency. To install the development dependencies, run the following command:

```
pip install -r requirements-dev.txt
```

### Managing Dependencies

When introducing new dependencies, make sure to add them to the appropriate `requirements.in` file (e.g., `requirements.in` for main dependencies, `requirements-dev.in` for development dependencies). Then, run the following commands to update the corresponding `requirements.txt` file:

```
pip install pip-tools
./scripts/pip-compile.sh
```

You can also pass one argument to `pip-compile.sh`, which will flow through to `pip-compile`. For example:

```
./scripts/pip-compile.sh --upgrade
```

### Pre-commit Hooks

The project uses [pre-commit](https://pre-commit.com/) hooks to automatically format code, lint, and run other checks before committing changes. After cloning the repository, run the following command to set up the pre-commit hooks:

```
pre-commit install
```

pre-commit will then run automatically on each `git commit` command. You can use the following command line to run pre-commit manually:

```
pre-commit run --all-files
```
