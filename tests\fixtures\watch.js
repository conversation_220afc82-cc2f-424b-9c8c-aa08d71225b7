// Regular AI comment
//ai do 1 something
//AI make 2 this better
//ai! urgent 3 change needed
//AI! another 4 urgent one
// ai with 5 space
// AI with 6 caps
// ai! with 7 bang
// this is not an ai comment
// aider is not an ai! comment

function dummyFunction() {
    // ai inside 8 function
    return true;
}

class Example {
    constructor() {
        // ai in 9 constructor
        this.value = 42;
    }

    method() {
        // ai in 10 method
        return this.value;
    }
  // ai!
  //ai
  //ai!
  // 11-13

  method2() { // ai 14
    return 1;
  }
}
// trailing whitespace ai  
// trailing whitespace ai!  
// 15-16
