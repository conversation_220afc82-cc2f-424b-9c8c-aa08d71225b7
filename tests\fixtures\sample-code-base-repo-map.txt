
tests/fixtures/sample-code-base/sample.js:
⋮
│function greet(name) {
│    return `Hello, ${name}!`;
⋮
│function calculateCircleArea(radius) {
│    return Math.PI * radius * radius;
⋮
│function isPrime(number) {
│    if (number <= 1) return false;
│    for (let i = 2; i <= Math.sqrt(number); i++) {
│        if (number % i === 0) return false;
│    }
│    return true;
⋮
│function reverseString(str) {
│    return str.split('').reverse().join('');
⋮
│function getRandomNumber(min, max) {
│    return Math.floor(Math.random() * (max - min + 1)) + min;
⋮
│function filterEvenNumbers(numbers) {
│    return numbers.filter(num => num % 2 !== 0);
⋮
│function factorial(n) {
│    if (n === 0 || n === 1) return 1;
│    return n * factorial(n - 1);
⋮

tests/fixtures/sample-code-base/sample.py:
│class Car:
│    def __init__(self, make, model, year):
│        self.make = make
│        self.model = model
│        self.year = year
⋮
│    def accelerate(self, increment):
⋮
│    def brake(self, decrement):
⋮
│    def honk(self):
⋮
│class Garage:
│    def __init__(self):
⋮
│    def add_car(self, car):
⋮
│    def remove_car(self, car):
⋮
│    def list_cars(self):
⋮
│def main():
⋮

