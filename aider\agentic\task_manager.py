"""
Task management system for agentic coding workflows.
"""
import uuid
from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Dict, Any
import json
from datetime import datetime


class TaskStatus(Enum):
    """Status of a coding task."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """Represents a single coding task."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    status: TaskStatus = TaskStatus.PENDING
    priority: int = 1  # 1=high, 2=medium, 3=low
    dependencies: List[str] = field(default_factory=list)  # Task IDs this depends on
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "status": self.status.value,
            "priority": self.priority,
            "dependencies": self.dependencies,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error,
            "metadata": self.metadata,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """Create task from dictionary."""
        task = cls(
            id=data["id"],
            title=data["title"],
            description=data["description"],
            status=TaskStatus(data["status"]),
            priority=data["priority"],
            dependencies=data["dependencies"],
            result=data.get("result"),
            error=data.get("error"),
            metadata=data.get("metadata", {}),
        )
        task.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("started_at"):
            task.started_at = datetime.fromisoformat(data["started_at"])
        if data.get("completed_at"):
            task.completed_at = datetime.fromisoformat(data["completed_at"])
        return task


class TaskManager:
    """Manages a collection of coding tasks with dependencies and progress tracking."""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.current_task_id: Optional[str] = None
        
    def add_task(self, task: Task) -> str:
        """Add a task to the manager."""
        self.tasks[task.id] = task
        return task.id
    
    def create_task(
        self,
        title: str,
        description: str,
        priority: int = 1,
        dependencies: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Create and add a new task."""
        task = Task(
            title=title,
            description=description,
            priority=priority,
            dependencies=dependencies or [],
            metadata=metadata or {},
        )
        return self.add_task(task)
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID."""
        return self.tasks.get(task_id)
    
    def update_task_status(self, task_id: str, status: TaskStatus, result: Optional[str] = None, error: Optional[str] = None):
        """Update task status and related fields."""
        task = self.tasks.get(task_id)
        if not task:
            return
            
        task.status = status
        now = datetime.now()
        
        if status == TaskStatus.IN_PROGRESS and not task.started_at:
            task.started_at = now
        elif status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
            task.completed_at = now
            
        if result:
            task.result = result
        if error:
            task.error = error
    
    def get_ready_tasks(self) -> List[Task]:
        """Get tasks that are ready to be executed (dependencies satisfied)."""
        ready_tasks = []
        for task in self.tasks.values():
            if task.status != TaskStatus.PENDING:
                continue
                
            # Check if all dependencies are completed
            dependencies_satisfied = True
            for dep_id in task.dependencies:
                dep_task = self.tasks.get(dep_id)
                if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                    dependencies_satisfied = False
                    break
                    
            if dependencies_satisfied:
                ready_tasks.append(task)
                
        # Sort by priority (1=high, 2=medium, 3=low)
        ready_tasks.sort(key=lambda t: t.priority)
        return ready_tasks
    
    def get_next_task(self) -> Optional[Task]:
        """Get the next task to execute."""
        ready_tasks = self.get_ready_tasks()
        return ready_tasks[0] if ready_tasks else None
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """Get all tasks with a specific status."""
        return [task for task in self.tasks.values() if task.status == status]
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a summary of task progress."""
        total = len(self.tasks)
        if total == 0:
            return {"total": 0, "completed": 0, "failed": 0, "pending": 0, "in_progress": 0, "progress_percent": 0}
            
        completed = len(self.get_tasks_by_status(TaskStatus.COMPLETED))
        failed = len(self.get_tasks_by_status(TaskStatus.FAILED))
        pending = len(self.get_tasks_by_status(TaskStatus.PENDING))
        in_progress = len(self.get_tasks_by_status(TaskStatus.IN_PROGRESS))
        
        progress_percent = (completed / total) * 100 if total > 0 else 0
        
        return {
            "total": total,
            "completed": completed,
            "failed": failed,
            "pending": pending,
            "in_progress": in_progress,
            "progress_percent": progress_percent,
        }
    
    def is_complete(self) -> bool:
        """Check if all tasks are completed or failed."""
        for task in self.tasks.values():
            if task.status in (TaskStatus.PENDING, TaskStatus.IN_PROGRESS):
                return False
        return True
    
    def clear(self):
        """Clear all tasks."""
        self.tasks.clear()
        self.current_task_id = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task manager to dictionary for serialization."""
        return {
            "tasks": {task_id: task.to_dict() for task_id, task in self.tasks.items()},
            "current_task_id": self.current_task_id,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TaskManager":
        """Create task manager from dictionary."""
        manager = cls()
        manager.current_task_id = data.get("current_task_id")
        for task_id, task_data in data.get("tasks", {}).items():
            task = Task.from_dict(task_data)
            manager.tasks[task_id] = task
        return manager
    
    def save_to_file(self, filepath: str):
        """Save task manager state to file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> "TaskManager":
        """Load task manager state from file."""
        with open(filepath, 'r') as f:
            data = json.load(f)
        return cls.from_dict(data)
