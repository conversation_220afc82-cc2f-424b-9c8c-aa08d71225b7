"""
Pydantic AI agents for task decomposition and execution.
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models import Model

from .task_manager import Task, TaskManager, TaskStatus


class TaskDecompositionRequest(BaseModel):
    """Request for task decomposition."""
    user_request: str
    context: Dict[str, Any] = {}


class TaskDecompositionResponse(BaseModel):
    """Response from task decomposition."""
    tasks: List[Dict[str, Any]]
    reasoning: str


class TaskExecutionRequest(BaseModel):
    """Request for task execution."""
    task: Dict[str, Any]
    context: Dict[str, Any] = {}


class TaskExecutionResponse(BaseModel):
    """Response from task execution."""
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None
    reasoning: str


class AgentContext:
    """Context shared between agents."""
    def __init__(self, coder=None, io=None, repo=None):
        self.coder = coder
        self.io = io
        self.repo = repo
        self.task_manager = TaskManager()


# Task Decomposition Agent
task_decomposition_agent = Agent(
    model=None,  # Will be set dynamically
    result_type=TaskDecompositionResponse,
    system_prompt="""You are an expert software development task decomposition agent.

Your role is to analyze user requests for coding tasks and break them down into specific, actionable subtasks.

Guidelines for task decomposition:
1. Each task should be specific and measurable
2. Tasks should be ordered by dependencies (prerequisite tasks first)
3. Each task should take approximately 10-30 minutes for a skilled developer
4. Include clear success criteria for each task
5. Consider the existing codebase context when creating tasks
6. Identify any dependencies between tasks
7. Prioritize tasks appropriately (1=high, 2=medium, 3=low)

For each task, provide:
- title: A clear, concise title
- description: Detailed description with acceptance criteria
- priority: 1 (high), 2 (medium), or 3 (low)
- dependencies: List of task titles this depends on
- metadata: Any additional context or requirements

Always provide reasoning for your decomposition approach.""",
)


@task_decomposition_agent.tool
def analyze_codebase_context(ctx: RunContext[AgentContext]) -> str:
    """Analyze the current codebase context to inform task decomposition."""
    if not ctx.deps.coder:
        return "No codebase context available"
    
    context_info = []
    
    # Get current files in context
    if hasattr(ctx.deps.coder, 'abs_fnames') and ctx.deps.coder.abs_fnames:
        context_info.append(f"Files in context: {len(ctx.deps.coder.abs_fnames)}")
        context_info.append(f"File list: {[ctx.deps.coder.get_rel_fname(f) for f in list(ctx.deps.coder.abs_fnames)[:10]]}")
    
    # Get repo information
    if ctx.deps.repo:
        context_info.append(f"Repository root: {ctx.deps.repo.root}")
        if hasattr(ctx.deps.repo, 'get_tracked_files'):
            tracked_files = ctx.deps.repo.get_tracked_files()
            context_info.append(f"Total tracked files: {len(tracked_files)}")
    
    return "\n".join(context_info)


# Task Execution Agent
task_execution_agent = Agent(
    model=None,  # Will be set dynamically
    result_type=TaskExecutionResponse,
    system_prompt="""You are an expert software development task execution agent.

Your role is to execute specific coding tasks using the available tools and codebase context.

Guidelines for task execution:
1. Understand the task requirements thoroughly
2. Use the available coder tools to make necessary changes
3. Follow existing code patterns and conventions
4. Test changes when possible
5. Provide clear feedback on what was accomplished
6. If a task cannot be completed, explain why and suggest alternatives

Always provide reasoning for your execution approach and results.""",
)


@task_execution_agent.tool
def execute_coder_command(ctx: RunContext[AgentContext], command: str) -> str:
    """Execute a coder command or make code changes."""
    if not ctx.deps.coder:
        return "No coder available"

    try:
        # Check if it's a slash command
        if command.startswith('/'):
            if hasattr(ctx.deps.coder, 'commands'):
                result = ctx.deps.coder.commands.run(command)
                return f"Command executed: {command}\nResult: {result}"
            else:
                return f"Commands not available on coder"
        else:
            # Treat as a regular message to send to the coder
            result = ctx.deps.coder.run(with_message=command, preproc=True)
            return f"Message sent to coder: {command}\nResult: {result}"
    except Exception as e:
        return f"Error executing command: {str(e)}"


@task_execution_agent.tool
def get_file_content(ctx: RunContext[AgentContext], filepath: str) -> str:
    """Get the content of a file."""
    if not ctx.deps.coder:
        return "No coder available"
    
    try:
        # Use the coder's file reading capabilities
        if hasattr(ctx.deps.coder, 'io') and hasattr(ctx.deps.coder.io, 'read_text'):
            return ctx.deps.coder.io.read_text(filepath)
        else:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
    except Exception as e:
        return f"Error reading file: {str(e)}"


@task_execution_agent.tool
def write_file_content(ctx: RunContext[AgentContext], filepath: str, content: str) -> str:
    """Write content to a file."""
    if not ctx.deps.coder:
        return "No coder available"
    
    try:
        # Use the coder's file writing capabilities
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        return f"Successfully wrote to {filepath}"
    except Exception as e:
        return f"Error writing file: {str(e)}"


@task_execution_agent.tool
def run_shell_command(ctx: RunContext[AgentContext], command: str) -> str:
    """Run a shell command."""
    if not ctx.deps.coder:
        return "No coder available"

    try:
        # Use the coder's command system to run shell commands
        if hasattr(ctx.deps.coder, 'commands'):
            # Use the /run command to execute shell commands
            result = ctx.deps.coder.commands.cmd_run(command)
            return f"Shell command executed: {command}\nResult: {result}"
        else:
            return f"Shell command execution not available"
    except Exception as e:
        return f"Error executing shell command: {str(e)}"


@task_execution_agent.tool
def add_files_to_chat(ctx: RunContext[AgentContext], file_paths: List[str]) -> str:
    """Add files to the chat context."""
    if not ctx.deps.coder:
        return "No coder available"

    try:
        if hasattr(ctx.deps.coder, 'commands'):
            results = []
            for file_path in file_paths:
                result = ctx.deps.coder.commands.cmd_add(file_path)
                results.append(f"Added {file_path}: {result}")
            return "\n".join(results)
        else:
            return "Add command not available"
    except Exception as e:
        return f"Error adding files: {str(e)}"


@task_execution_agent.tool
def run_tests(ctx: RunContext[AgentContext], test_command: Optional[str] = None) -> str:
    """Run tests using the coder's test functionality."""
    if not ctx.deps.coder:
        return "No coder available"

    try:
        if hasattr(ctx.deps.coder, 'commands'):
            result = ctx.deps.coder.commands.cmd_test(test_command)
            return f"Test execution result: {result}"
        else:
            return "Test command not available"
    except Exception as e:
        return f"Error running tests: {str(e)}"


@task_execution_agent.tool
def commit_changes(ctx: RunContext[AgentContext], message: str) -> str:
    """Commit changes to git."""
    if not ctx.deps.coder:
        return "No coder available"

    try:
        if hasattr(ctx.deps.coder, 'commands'):
            result = ctx.deps.coder.commands.cmd_commit(message)
            return f"Commit result: {result}"
        else:
            return "Commit command not available"
    except Exception as e:
        return f"Error committing changes: {str(e)}"


class TaskDecompositionAgent:
    """Wrapper for the task decomposition agent."""
    
    def __init__(self, model: Model):
        self.agent = task_decomposition_agent
        self.agent.model = model
    
    async def decompose_task(self, user_request: str, context: AgentContext) -> TaskDecompositionResponse:
        """Decompose a user request into specific tasks."""
        result = await self.agent.run(
            f"Decompose this coding request into specific tasks: {user_request}",
            deps=context
        )

        return result.data


class TaskExecutionAgent:
    """Wrapper for the task execution agent."""
    
    def __init__(self, model: Model):
        self.agent = task_execution_agent
        self.agent.model = model
    
    async def execute_task(self, task: Task, context: AgentContext) -> TaskExecutionResponse:
        """Execute a specific task."""
        result = await self.agent.run(
            f"Execute this coding task: {task.title}\n\nDescription: {task.description}",
            deps=context
        )

        return result.data
