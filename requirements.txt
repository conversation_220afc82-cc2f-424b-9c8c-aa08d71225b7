# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements.txt requirements/requirements.in
aiohappyeyeballs==2.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
aiohttp==3.11.18
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
aiosignal==1.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
annotated-types==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
anthropic==0.57.1
    # via pydantic-ai-slim
anyio==4.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   google-genai
    #   groq
    #   httpx
    #   mcp
    #   openai
    #   pydantic-evals
    #   sse-starlette
    #   starlette
    #   watchfiles
argcomplete==3.6.2
    # via pydantic-ai-slim
attrs==25.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   posthog
beautifulsoup4==4.13.4
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
boto3==1.39.4
    # via pydantic-ai-slim
botocore==1.39.4
    # via
    #   boto3
    #   s3transfer
cachetools==5.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
certifi==2025.4.26
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   -c requirements/common-constraints.txt
    #   sounddevice
    #   soundfile
charset-normalizer==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
    #   uvicorn
cohere==5.16.1
    # via pydantic-ai-slim
colorama==0.4.6
    # via
    #   -c requirements/common-constraints.txt
    #   click
    #   griffe
    #   tqdm
configargparse==1.7.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
diff-match-patch==20241021
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
diskcache==5.6.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
distro==1.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   groq
    #   openai
    #   posthog
eval-type-backport==0.2.2
    # via
    #   mistralai
    #   pydantic-ai-slim
fasta2a==0.2.20
    # via pydantic-ai-slim
fastavro==1.11.1
    # via cohere
filelock==3.18.0
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
flake8==7.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
frozenlist==1.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
gitdb==4.0.12
    # via
    #   -c requirements/common-constraints.txt
    #   gitpython
gitpython==3.1.44
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
google-ai-generativelanguage==0.6.15
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
google-api-core[grpc]==2.24.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.169.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
google-auth==2.40.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-genai
    #   google-generativeai
    #   pydantic-ai-slim
google-auth-httplib2==0.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
google-genai==1.20.0
    # via pydantic-ai-slim
google-generativeai==0.8.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
googleapis-common-protos==1.70.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grep-ast==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
griffe==1.7.3
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic-ai-slim
groq==0.30.0
    # via pydantic-ai-slim
grpcio==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
h11==0.16.0
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via
    #   -c requirements/common-constraints.txt
    #   httpx
httplib2==0.22.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   cohere
    #   google-genai
    #   groq
    #   litellm
    #   mcp
    #   mistralai
    #   openai
    #   pydantic-ai-slim
    #   pydantic-graph
httpx-sse==0.4.0
    # via
    #   cohere
    #   mcp
huggingface-hub==0.31.1
    # via
    #   -c requirements/common-constraints.txt
    #   tokenizers
idna==3.10
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==7.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   litellm
    #   opentelemetry-api
importlib-resources==6.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
jinja2==3.1.6
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
jiter==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   openai
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
json5==0.12.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
jsonschema==4.23.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   litellm
    #   mcp
jsonschema-specifications==2025.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
litellm==1.68.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
logfire-api==3.24.1
    # via
    #   pydantic-evals
    #   pydantic-graph
markdown-it-py==3.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   rich
markupsafe==3.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   jinja2
mccabe==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
mcp==1.11.0
    # via pydantic-ai-slim
mdurl==0.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   markdown-it-py
mistralai==1.9.2
    # via pydantic-ai-slim
mixpanel==4.10.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
mslex==1.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   oslex
multidict==6.4.3
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
networkx==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
numpy==1.26.4
    # via
    #   -c requirements/common-constraints.txt
    #   scipy
    #   soundfile
openai==1.75.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
    #   pydantic-ai-slim
opentelemetry-api==1.35.0
    # via
    #   fasta2a
    #   pydantic-ai-slim
oslex==0.1.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
packaging==24.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   huggingface-hub
pathspec==0.12.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   grep-ast
pexpect==4.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pillow==11.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
posthog==4.0.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
prompt-toolkit==3.0.51
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   pydantic-ai-slim
propcache==0.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.4
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psutil==7.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
ptyprocess==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   pexpect
pyasn1==0.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
pycodestyle==2.13.0
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
pycparser==2.22
    # via
    #   -c requirements/common-constraints.txt
    #   cffi
pydantic==2.11.4
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   cohere
    #   fasta2a
    #   google-genai
    #   google-generativeai
    #   groq
    #   litellm
    #   mcp
    #   mistralai
    #   openai
    #   pydantic-ai-slim
    #   pydantic-evals
    #   pydantic-graph
    #   pydantic-settings
pydantic-ai==0.2.20
    # via -r requirements/requirements.in
pydantic-ai-slim[a2a, anthropic, bedrock, cli, cohere, evals, google, groq, mcp, mistral, openai, vertexai]==0.2.20
    # via
    #   pydantic-ai
    #   pydantic-evals
pydantic-core==2.33.2
    # via
    #   -c requirements/common-constraints.txt
    #   cohere
    #   pydantic
pydantic-evals==0.2.20
    # via pydantic-ai-slim
pydantic-graph==0.2.20
    # via pydantic-ai-slim
pydantic-settings==2.10.1
    # via mcp
pydub==0.25.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pyflakes==3.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
pygments==2.19.1
    # via
    #   -c requirements/common-constraints.txt
    #   rich
pypandoc==1.15
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pyparsing==3.2.3
    # via
    #   -c requirements/common-constraints.txt
    #   httplib2
pyperclip==1.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
python-dateutil==2.9.0.post0
    # via
    #   -c requirements/common-constraints.txt
    #   botocore
    #   mistralai
    #   posthog
python-dotenv==1.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
    #   pydantic-settings
python-multipart==0.0.20
    # via mcp
pywin32==310
    # via mcp
pyyaml==6.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   huggingface-hub
    #   pydantic-evals
referencing==0.36.2
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   -c requirements/common-constraints.txt
    #   tiktoken
requests==2.32.3
    # via
    #   -c requirements/common-constraints.txt
    #   cohere
    #   google-api-core
    #   google-genai
    #   huggingface-hub
    #   mixpanel
    #   posthog
    #   pydantic-ai-slim
    #   tiktoken
rich==14.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   pydantic-ai-slim
    #   pydantic-evals
rpds-py==0.24.0
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
s3transfer==0.13.0
    # via boto3
scipy==1.15.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
shtab==1.7.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
six==1.17.0
    # via
    #   -c requirements/common-constraints.txt
    #   mixpanel
    #   posthog
    #   python-dateutil
smmap==5.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   gitdb
sniffio==1.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   anyio
    #   groq
    #   openai
socksio==1.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
sounddevice==0.5.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
soundfile==0.13.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
soupsieve==2.7
    # via
    #   -c requirements/common-constraints.txt
    #   beautifulsoup4
sse-starlette==2.4.1
    # via mcp
starlette==0.47.1
    # via
    #   fasta2a
    #   mcp
tiktoken==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
tokenizers==0.21.1
    # via
    #   -c requirements/common-constraints.txt
    #   cohere
    #   litellm
tqdm==4.67.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
    #   huggingface-hub
    #   openai
tree-sitter==0.24.0
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-c-sharp==0.23.1
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-embedded-template==0.23.2
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-language-pack==0.7.3
    # via
    #   -c requirements/common-constraints.txt
    #   grep-ast
tree-sitter-yaml==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
types-requests==2.32.4.20250611
    # via cohere
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   anthropic
    #   anyio
    #   beautifulsoup4
    #   cohere
    #   google-genai
    #   google-generativeai
    #   groq
    #   huggingface-hub
    #   openai
    #   opentelemetry-api
    #   pydantic
    #   pydantic-core
    #   referencing
    #   starlette
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   mistralai
    #   pydantic
    #   pydantic-ai-slim
    #   pydantic-graph
    #   pydantic-settings
uritemplate==4.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
urllib3==2.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   botocore
    #   mixpanel
    #   requests
    #   types-requests
uvicorn==0.35.0
    # via mcp
watchfiles==1.0.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
wcwidth==0.2.13
    # via
    #   -c requirements/common-constraints.txt
    #   prompt-toolkit
websockets==15.0.1
    # via google-genai
yarl==1.20.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
zipp==3.21.0
    # via
    #   -c requirements/common-constraints.txt
    #   importlib-metadata
